# frozen_string_literal: true

module SmartEnergy
  module Api
    module V1
      module Entities
        class ClientableEntity < Grape::Entity
          # Dynamically represent clientable objects with appropriate fields
          def self.represent_clientable(client)
            entity = Class.new(Grape::Entity) do
              if client.person?
                include SmartEnergy::Api::V1::Entities::Fields::PersonFields
              elsif client.company?
                include SmartEnergy::Api::V1::Entities::Fields::CompanyFields
              else
                raise "Unknown clientable type: #{client.clientable_type}"
              end
            end
            entity.represent(client.clientable)
          end
        end
      end
    end
  end
end
